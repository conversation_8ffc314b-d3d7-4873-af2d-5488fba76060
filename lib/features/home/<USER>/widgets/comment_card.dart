import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;

class CommentCard extends StatelessWidget {
  final entities.Question question;
  final String? selectedCommentType;
  final TextEditingController commentController;
  final ValueChanged<String?> onCommentTypeChanged;
  final String? errorText;
  final bool isCompleted;

  const CommentCard({
    super.key,
    required this.question,
    required this.selectedCommentType,
    required this.commentController,
    required this.onCommentTypeChanged,
    this.errorText,
    this.isCompleted = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final List<entities.CommentType> commentTypes = question.commentTypes ?? [];

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.completedGreenDark : Colors.white,
        borderRadius: BorderRadius.circular(10.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and subtitle
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title with mandatory indicator
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        question.questionDescription ??
                            'General comments / feedback',
                        style: textTheme.montserratTitleExtraSmall,
                      ),
                    ),
                    if (question.isCommentMandatory == true)
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.transparent,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.priority_high,
                          color: AppColors.loginRed,
                          size: 16,
                        ),
                      ),
                  ],
                ),
                const Gap(4),
                // Text(
                //   question.isCommentMandatory == true ? 'Required' : 'Optional',
                //   style: textTheme.montserratTableSmall.copyWith(
                //     color: AppColors.blackTint1,
                //   ),
                // ),
              ],
            ),
            const Gap(16),
            // Dropdown
            if (commentTypes.isNotEmpty)
              DropdownButtonFormField<String>(
                borderRadius: BorderRadius.circular(10.0),
                dropdownColor: Colors.white,
                decoration: InputDecoration(
                  hintText: 'Select...',
                  hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.blackTint1,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10.0),
                    borderSide: BorderSide(
                      color:
                          errorText != null ? Colors.red : AppColors.blackTint2,
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10.0),
                    borderSide: BorderSide(
                      color:
                          errorText != null ? Colors.red : AppColors.blackTint2,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(10.0),
                    borderSide: BorderSide(
                      color: errorText != null
                          ? Colors.red
                          : AppColors.primaryBlue,
                      width: 2,
                    ),
                  ),
                  errorStyle: textTheme.montserratTableSmall.copyWith(
                    color: Colors.red,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                value: selectedCommentType,
                isExpanded: true,
                items: commentTypes.map((commentType) {
                  return DropdownMenuItem<String>(
                    value: commentType.commentType,
                    child: Text(
                      commentType.commentType ?? 'Unnamed Type',
                      style: textTheme.montserratTitleExtraSmall.copyWith(
                        color: AppColors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: onCommentTypeChanged,
              ),
            if (commentTypes.isNotEmpty) const Gap(16),
            // Comment text field
            TextFormField(
              controller: commentController,
              decoration: InputDecoration(
                hintText: 'Comment',
                hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                  color: AppColors.blackTint1,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color:
                        errorText != null ? Colors.red : AppColors.blackTint2,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color:
                        errorText != null ? Colors.red : AppColors.blackTint2,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10.0),
                  borderSide: BorderSide(
                    color:
                        errorText != null ? Colors.red : AppColors.primaryBlue,
                    width: 2,
                  ),
                ),
                errorStyle: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              ),
              style: textTheme.montserratTitleExtraSmall.copyWith(
                color: AppColors.black,
              ),
              maxLines: 4,
            ),
            // Error text display
            if (errorText != null) ...[
              const Gap(8),
              Text(
                errorText!,
                style: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
